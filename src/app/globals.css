@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));
  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-destructive: hsl(var(--destructive));
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: hsl(var(--sidebar-foreground));
  --color-sidebar-primary: hsl(var(--sidebar-primary));
  --color-sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));
  --color-sidebar-accent: hsl(var(--sidebar-accent));
  --color-sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
  --color-sidebar-border: hsl(var(--sidebar-border));
  --color-sidebar-ring: hsl(var(--sidebar-ring));
  --bg-gradient-hero: hsl(var(--gradient-hero));
  --bg-gradient-card: hsl(var(--gradient-card));
  --bg-gradient-level: hsl(var(--gradient-level));
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-fade-in: fade-in 0.5s ease-out;
  --animate-slide-up: slide-up 0.6s ease-out;
  --animate-level-up: level-up 0.6s ease-in-out;
  --animate-pulse-glow: pulse-glow 2s ease-in-out infinite;
}

:root {
  --radius: 0.75rem;
  --background: 220 25% 97%;
  --foreground: 220 20% 10%;
  --card: 0 0% 100%;
  --card-foreground: 220 20% 10%;
  --popover: 0 0% 100%;
  --popover-foreground: 220 20% 10%;
  --primary: 220 90% 56%;
  --primary-foreground: 0 0% 100%;
  --primary-glow: 220 95% 65%;
  --secondary: 160 84% 45%;
  --secondary-foreground: 0 0% 100%;
  --secondary-glow: 160 90% 55%;
  --muted: 220 20% 92%;
  --muted-foreground: 220 15% 45%;
  --accent: 30 95% 55%;
  --accent-foreground: 0 0% 100%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 100%;
  --border: 220 20% 88%;
  --input: 220 20% 88%;
  --ring: 220 90% 56%;
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-background: 0 0% 98%;
  --sidebar-foreground: 240 5.3% 26.1%;
  --sidebar-primary: 240 5.9% 10%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 240 4.8% 95.9%;
  --sidebar-accent-foreground: 240 5.9% 10%;
  --sidebar-border: 220 13% 91%;
  --sidebar-ring: 220 90% 56%;
  --gradient-hero: linear-gradient(
    135deg,
    hsl(220 90% 56%) 0%,
    hsl(160 84% 45%) 100%
  );
  --gradient-card: linear-gradient(
    145deg,
    hsl(220 95% 65% / 0.05) 0%,
    hsl(160 90% 55% / 0.05) 100%
  );
  --gradient-level: linear-gradient(
    90deg,
    hsl(30 95% 55%) 0%,
    hsl(0 95% 60%) 100%
  );
  --shadow-glow: 0 0 40px hsl(220 95% 65% / 0.3);
  --shadow-card: 0 4px 20px hsl(220 20% 10% / 0.08);
  --shadow-elevated: 0 8px 30px hsl(220 20% 10% / 0.12);
}

.dark {
  --background: 220 25% 8%;
  --foreground: 220 20% 95%;
  --card: 220 20% 12%;
  --card-foreground: 220 20% 95%;
  --popover: 220 20% 12%;
  --popover-foreground: 220 20% 95%;
  --primary: 220 90% 60%;
  --primary-foreground: 220 25% 8%;
  --primary-glow: 220 95% 70%;
  --secondary: 160 84% 50%;
  --secondary-foreground: 220 25% 8%;
  --secondary-glow: 160 90% 60%;
  --muted: 220 20% 18%;
  --muted-foreground: 220 15% 60%;
  --accent: 30 95% 60%;
  --accent-foreground: 220 25% 8%;
  --destructive: 0 84% 55%;
  --destructive-foreground: 220 20% 95%;
  --border: 220 20% 20%;
  --input: 220 20% 20%;
  --ring: 220 90% 60%;
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-background: 240 5.9% 10%;
  --sidebar-foreground: 240 4.8% 95.9%;
  --sidebar-primary: 224.3 76.3% 48%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 240 3.7% 15.9%;
  --sidebar-accent-foreground: 240 4.8% 95.9%;
  --sidebar-border: 240 3.7% 15.9%;
  --sidebar-ring: 220 90% 60%;
  --gradient-hero: linear-gradient(
    135deg,
    hsl(220 90% 35%) 0%,
    hsl(160 84% 30%) 100%
  );
  --gradient-card: linear-gradient(
    145deg,
    hsl(220 95% 65% / 0.08) 0%,
    hsl(160 90% 55% / 0.08) 100%
  );
  --gradient-level: linear-gradient(
    90deg,
    hsl(30 95% 50%) 0%,
    hsl(0 95% 55%) 100%
  );
  --shadow-glow: 0 0 50px hsl(220 95% 70% / 0.2);
  --shadow-card: 0 4px 20px hsl(220 25% 5% / 0.5);
  --shadow-elevated: 0 8px 30px hsl(220 25% 5% / 0.7);
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}
@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}
@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes level-up {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes pulse-glow {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}
