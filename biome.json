{"$schema": "https://biomejs.dev/schemas/2.2.0/schema.json", "assist": {"actions": {"source": {"organizeImports": "on", "useSortedAttributes": "on", "useSortedKeys": "on", "useSortedProperties": "on"}}}, "files": {"ignoreUnknown": true, "includes": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "**/*.json", "**/*.md", "!node_modules", "!.next", "!dist", "!build", "!package.json", "!pnpm-*"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 100}, "javascript": {"formatter": {"arrowParentheses": "always", "bracketSameLine": false, "bracketSpacing": true, "jsxQuoteStyle": "double", "quoteStyle": "double", "semicolons": "always", "trailingCommas": "es5"}}, "linter": {"domains": {"next": "recommended", "react": "recommended"}, "enabled": true, "rules": {"complexity": {"noExtraBooleanCast": "error"}, "correctness": {"noUnusedVariables": "error", "useExhaustiveDependencies": "warn", "useHookAtTopLevel": "error"}, "recommended": true, "style": {"noParameterAssign": "warn", "useConst": "error", "useTemplate": "warn"}, "suspicious": {"noConsole": {"level": "warn", "options": {"allow": ["warn", "error"]}}, "noDebugger": "error", "noDoubleEquals": "error", "noExplicitAny": "error"}}}, "vcs": {"clientKind": "git", "enabled": true, "useIgnoreFile": true}}